/**
 * ArchitectView - Main 3-panel layout for CodeQuilter Architect Mode
 * 
 * Layout:
 * - Left Panel: FileExplorer (project structure)
 * - Center Panel: CodeEditor (Monaco editor)
 * - Right Panel: AgentPanel (Task List UI)
 */

import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useProjectStore } from '../../stores/projectStore';
import { FileExplorer } from './FileExplorer';
import { CodeEditor } from './CodeEditor';
import { AgentPanel } from './AgentPanel';
import { LoadingSpinner } from '../ui';
import { generateSessionId } from '../../lib/utils';

export const ArchitectView: React.FC = () => {
  const { sessionId: urlSessionId } = useParams<{ sessionId?: string }>();
  const [sessionId, setSessionId] = useState<string>(urlSessionId || generateSessionId());

  const {
    projectState,
    isLoading,
    error,
    loadProject,
    createProject,
    connectWebSocket,
    disconnectWebSocket,
    clearError,
  } = useProjectStore();

  // Initialize project and WebSocket connection
  useEffect(() => {
    const initializeProject = async () => {
      if (urlSessionId) {
        // Load existing project
        await loadProject(urlSessionId);
      }
      // Don't auto-create project - let user start with "Start New Project" button
    };

    initializeProject();
  }, [urlSessionId, loadProject]);

  // Connect WebSocket when project is loaded
  useEffect(() => {
    if (projectState?.session_id) {
      connectWebSocket(projectState.session_id);

      // Cleanup on unmount
      return () => {
        disconnectWebSocket();
      };
    }
  }, [projectState?.session_id, connectWebSocket, disconnectWebSocket]);

  // Loading state
  if (isLoading && !projectState) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-gray-600">Loading CodeQuilter...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !projectState) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="rounded-full bg-error-100 p-3">
            <svg className="h-6 w-6 text-error-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Error Loading Project</h3>
            <p className="text-gray-600">{error}</p>
          </div>
          <button
            onClick={clearError}
            className="rounded-md bg-primary-600 px-4 py-2 text-white hover:bg-primary-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Panel - File Explorer */}
      <div className="w-80 border-r border-gray-200 bg-white">
        <FileExplorer />
      </div>

      {/* Center Panel - Code Editor */}
      <div className="flex-1 flex flex-col">
        <CodeEditor />
      </div>

      {/* Right Panel - Agent Panel (Task List) */}
      <div className="w-96 border-l border-gray-200 bg-white">
        <AgentPanel />
      </div>
    </div>
  );
};
