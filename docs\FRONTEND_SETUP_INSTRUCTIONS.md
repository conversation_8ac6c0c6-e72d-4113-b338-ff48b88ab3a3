# CodeQuilter Frontend Setup Instructions

## Prerequisites

### Node.js Installation Required

The CodeQuilter frontend requires Node.js to be installed to run the development server and build the application.

**To install Node.js:**

1. **Download Node.js** from the official website: https://nodejs.org/
   - Choose the LTS (Long Term Support) version
   - For Windows: Download the Windows Installer (.msi)

2. **Install Node.js**
   - Run the downloaded installer
   - Follow the installation wizard
   - Make sure to check "Add to PATH" during installation

3. **Verify Installation**
   ```bash
   node --version
   npm --version
   ```

## Frontend Setup Steps

Once Node.js is installed, follow these steps:

### 1. Install Dependencies
```bash
cd frontend
npm install
```

### 2. Start Development Server
```bash
npm run dev
```

The frontend will be available at: http://localhost:3000

### 3. Backend Integration
Make sure the backend is running on port 8000:
```bash
cd backend
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
```

## Current Implementation Status

### ✅ Completed Components

1. **AgentPanel** - Complete Task List UI with state machine
   - Awaiting Approval state with plan rationale display
   - Executing state with real-time task progress
   - Complete/Failed state with suggested actions
   - Implements all three Gemini principles

2. **FileExplorer** - Project structure tree view
   - Expandable folder navigation
   - File type icons and size display
   - File selection integration with CodeEditor

3. **CodeEditor** - Enhanced code viewing component
   - Syntax highlighting placeholder (ready for Monaco)
   - Copy and download functionality
   - Read-only file viewing with language detection

4. **WebSocket Integration** - Real-time communication
   - Plan approval workflow
   - Task status updates
   - Connection management with reconnection
   - All backend tests passing

5. **UI Components** - Complete component library
   - Button, Badge, Card, LoadingSpinner
   - Proper TypeScript types
   - Tailwind CSS styling

### 🔄 Ready for Enhancement (Post Node.js Installation)

1. **Monaco Editor Integration**
   - Replace placeholder with full Monaco Editor
   - Syntax highlighting for all supported languages
   - Advanced code viewing features

2. **Testing Suite**
   - Vitest configuration
   - React Testing Library integration
   - Component and integration tests

3. **Build Process**
   - Production build optimization
   - Asset bundling and optimization

## Architecture Overview

### 3-Panel Layout
- **Left Panel**: FileExplorer (project structure)
- **Center Panel**: CodeEditor (Monaco editor)
- **Right Panel**: AgentPanel (Task List UI)

### State Management
- **Zustand Store**: Centralized state management
- **Real-time Sync**: WebSocket integration with backend
- **Type Safety**: Full TypeScript integration

### Communication Flow
```
Frontend (React) ←→ WebSocket ←→ Backend (FastAPI)
                 ↓
            TaskExecutionAgent
                 ↓
            Real-time Updates
```

## Next Steps

1. **Install Node.js** (see prerequisites above)
2. **Run `npm install`** in the frontend directory
3. **Start both backend and frontend servers**
4. **Test the complete application**

The frontend is fully implemented and ready to run once Node.js dependencies are installed.
