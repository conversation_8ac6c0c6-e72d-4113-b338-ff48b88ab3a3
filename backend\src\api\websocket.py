"""
WebSocket API for TaskExecutionAgent real-time communication.

Implements real-time task updates with the following message types:
- PLAN_GENERATED: Send task list for user approval
- TASK_STATUS_UPDATE: Real-time task progress updates  
- EXECUTION_COMPLETE: Final results with suggested actions

This enables transparent task execution with live user feedback.
"""

import json
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

from ..agents.task_execution_agent import TaskExecutionAgent
from ..agents.task_models import Task, ExecutionResult
from ..state.project_state import ProjectState
from ..session_manager import session_manager

logger = logging.getLogger(__name__)


class WebSocketMessageType:
    """WebSocket message types for TaskExecutionAgent communication"""
    PLAN_GENERATED = "PLAN_GENERATED"
    PLAN_APPROVED = "PLAN_APPROVED"
    PLAN_REJECTED = "PLAN_REJECTED"
    TASK_STATUS_UPDATE = "TASK_STATUS_UPDATE"
    EXECUTION_COMPLETE = "EXECUTION_COMPLETE"
    ERROR = "ERROR"
    PING = "PING"
    PONG = "PONG"


class TaskExecutionWebSocketManager:
    """
    Manages WebSocket connections for TaskExecutionAgent operations.
    
    Handles real-time communication between frontend and TaskExecutionAgent
    with proper connection management and message batching.
    """
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.task_agents: Dict[str, TaskExecutionAgent] = {}
        self.message_queue: Dict[str, List[Dict[str, Any]]] = {}
        self.batch_interval = 0.3  # 300ms batching for performance
        
    async def connect(self, websocket: WebSocket, session_id: str) -> None:
        """Accept WebSocket connection and initialize session"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        self.message_queue[session_id] = []
        
        # Create TaskExecutionAgent for this session
        agent = TaskExecutionAgent()
        agent.set_activity_callback(self._create_activity_callback(session_id))
        self.task_agents[session_id] = agent
        
        logger.info(f"WebSocket connected for session: {session_id}")
        
        # Send connection confirmation
        await self._send_message(session_id, {
            "type": "CONNECTION_ESTABLISHED",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "message": "TaskExecutionAgent WebSocket connected"
        })
    
    def disconnect(self, session_id: str) -> None:
        """Clean up WebSocket connection"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
        if session_id in self.task_agents:
            del self.task_agents[session_id]
        if session_id in self.message_queue:
            del self.message_queue[session_id]
        
        logger.info(f"WebSocket disconnected for session: {session_id}")
    
    def _create_activity_callback(self, session_id: str):
        """Create activity callback for TaskExecutionAgent"""
        def activity_callback(task_id: str, activity: str):
            # Create async task for message queuing
            asyncio.create_task(self._queue_message(session_id, {
                "type": WebSocketMessageType.TASK_STATUS_UPDATE,
                "task_id": task_id,
                "activity": activity,
                "timestamp": datetime.now().isoformat()
            }))
        return activity_callback
    
    async def handle_plan_request(self, session_id: str, goal: str, context: Dict[str, Any]) -> None:
        """Handle plan generation request"""
        try:
            agent = self.task_agents.get(session_id)
            if not agent:
                await self._send_error(session_id, "No TaskExecutionAgent found for session")
                return
            
            # Generate plan with rationale (Gemini Principle 1)
            tasks, rationale = await agent.plan(goal, context)
            
            # Send plan to frontend for approval
            await self._send_message(session_id, {
                "type": WebSocketMessageType.PLAN_GENERATED,
                "goal": goal,
                "rationale": rationale,  # Gemini Principle 1: Execution Rationale
                "tasks": [task.to_dict() for task in tasks],
                "task_count": len(tasks),
                "timestamp": datetime.now().isoformat()
            })
            
            logger.info(f"Plan generated for session {session_id}: {len(tasks)} tasks")
            
        except Exception as e:
            logger.error(f"Plan generation failed for session {session_id}: {e}")
            await self._send_error(session_id, f"Plan generation failed: {str(e)}")
    
    async def handle_plan_approval(self, session_id: str, approved: bool, tasks_data: List[Dict[str, Any]]) -> None:
        """Handle plan approval/rejection from user"""
        try:
            if not approved:
                await self._send_message(session_id, {
                    "type": "PLAN_REJECTED",
                    "message": "Plan rejected by user",
                    "timestamp": datetime.now().isoformat()
                })
                return
            
            # Convert task data back to Task objects
            tasks = [Task(
                id=task_data.get("id"),
                description=task_data.get("description"),
                metadata=task_data.get("metadata", {})
            ) for task_data in tasks_data]
            
            # Get project state
            project_state = session_manager.get_session(session_id)
            if not project_state:
                await self._send_error(session_id, "Project state not found")
                return
            
            # Execute tasks
            agent = self.task_agents[session_id]
            result = await agent.execute(tasks, project_state)
            
            # Send completion message with suggested actions (Gemini Principle 3)
            await self._send_message(session_id, {
                "type": WebSocketMessageType.EXECUTION_COMPLETE,
                "success": result.success,
                "summary_message": result.summary_message,
                "completed_tasks": [task.to_dict() for task in result.completed_tasks],
                "failed_task": result.failed_task.to_dict() if result.failed_task else None,
                "total_duration_seconds": result.total_duration_seconds,
                "suggested_actions": result.to_dict()["suggested_actions"],  # Gemini Principle 3
                "timestamp": datetime.now().isoformat()
            })
            
            logger.info(f"Task execution completed for session {session_id}: success={result.success}")
            
        except Exception as e:
            logger.error(f"Task execution failed for session {session_id}: {e}")
            await self._send_error(session_id, f"Task execution failed: {str(e)}")
    
    async def _queue_message(self, session_id: str, message: Dict[str, Any]) -> None:
        """Queue message for batched sending"""
        if session_id in self.message_queue:
            self.message_queue[session_id].append(message)
            
            # Start batch timer if this is the first message
            if len(self.message_queue[session_id]) == 1:
                asyncio.create_task(self._flush_message_queue(session_id))
    
    async def _flush_message_queue(self, session_id: str) -> None:
        """Flush queued messages after batch interval"""
        await asyncio.sleep(self.batch_interval)
        
        if session_id in self.message_queue and self.message_queue[session_id]:
            messages = self.message_queue[session_id].copy()
            self.message_queue[session_id].clear()
            
            # Send batched messages
            for message in messages:
                await self._send_message(session_id, message)
    
    async def _send_message(self, session_id: str, message: Dict[str, Any]) -> None:
        """Send message to WebSocket client"""
        websocket = self.active_connections.get(session_id)
        if websocket and websocket.client_state == WebSocketState.CONNECTED:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to send WebSocket message to {session_id}: {e}")
                self.disconnect(session_id)
    
    async def _send_error(self, session_id: str, error_message: str) -> None:
        """Send error message to client"""
        await self._send_message(session_id, {
            "type": WebSocketMessageType.ERROR,
            "error": error_message,
            "timestamp": datetime.now().isoformat()
        })
    
    async def handle_ping(self, session_id: str) -> None:
        """Handle ping for connection keepalive"""
        await self._send_message(session_id, {
            "type": WebSocketMessageType.PONG,
            "timestamp": datetime.now().isoformat()
        })


# Global WebSocket manager instance
websocket_manager = TaskExecutionWebSocketManager()


async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """
    WebSocket endpoint for TaskExecutionAgent communication.
    
    Handles real-time task execution with plan approval workflow.
    """
    await websocket_manager.connect(websocket, session_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "PLAN_REQUEST":
                goal = message.get("goal", "")
                context = message.get("context", {})
                await websocket_manager.handle_plan_request(session_id, goal, context)
                
            elif message_type == WebSocketMessageType.PLAN_APPROVED:
                approved = message.get("approved", False)
                tasks_data = message.get("tasks", [])
                await websocket_manager.handle_plan_approval(session_id, approved, tasks_data)
                
            elif message_type == WebSocketMessageType.PING:
                await websocket_manager.handle_ping(session_id)
                
            else:
                await websocket_manager._send_error(session_id, f"Unknown message type: {message_type}")
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for session: {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
    finally:
        websocket_manager.disconnect(session_id)
