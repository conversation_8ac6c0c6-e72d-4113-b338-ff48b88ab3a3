import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ArchitectView } from './components/architect/ArchitectView';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Navigate to="/architect" replace />} />
          <Route path="/architect" element={<ArchitectView />} />
          <Route path="/architect/:sessionId" element={<ArchitectView />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
