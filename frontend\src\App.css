/* CodeQuilter App Styles */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

.App {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Task status animations */
.task-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Panel resize handles */
.resize-handle {
  background: #e2e8f0;
  transition: background-color 0.2s;
}

.resize-handle:hover {
  background: #cbd5e1;
}

/* Monaco editor container */
.monaco-editor-container {
  height: 100%;
  width: 100%;
}

/* File tree styles */
.file-tree-item {
  transition: background-color 0.15s ease;
}

.file-tree-item:hover {
  background-color: #f8fafc;
}

.file-tree-item.selected {
  background-color: #e0f2fe;
  border-left: 3px solid #0ea5e9;
}
