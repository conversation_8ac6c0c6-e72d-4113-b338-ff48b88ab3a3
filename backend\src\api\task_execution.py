"""
REST API endpoints for TaskExecutionAgent operations.

Provides HTTP endpoints that integrate with the TaskExecutionAgent while maintaining
backward compatibility with existing APIs. Complex operations use TaskExecutionAgent
while simple operations continue using direct module calls.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from ..agents.task_execution_agent import TaskExecutionAgent
from ..agents.task_models import Task, ExecutionResult
from ..state.project_state import ProjectState
from ..session_manager import session_manager
from ..modules.component_discovery import ComponentMatcher
from ..modules.brainstorming import BrainstormingEngine

router = APIRouter(prefix="/api/projects/{session_id}/tasks", tags=["task-execution"])


class TaskPlanRequest(BaseModel):
    """Request model for task plan generation"""
    goal: str
    context: Dict[str, Any] = {}


class TaskPlanResponse(BaseModel):
    """Response model for task plan generation"""
    goal: str
    rationale: str  # Gemini Principle 1: Execution Rationale
    tasks: List[Dict[str, Any]]
    task_count: int


class TaskExecutionRequest(BaseModel):
    """Request model for task execution"""
    tasks: List[Dict[str, Any]]
    approved: bool = True


class TaskExecutionResponse(BaseModel):
    """Response model for task execution results"""
    success: bool
    summary_message: str
    completed_tasks: List[Dict[str, Any]]
    failed_task: Optional[Dict[str, Any]] = None
    total_duration_seconds: float
    suggested_actions: List[Dict[str, Any]]  # Gemini Principle 3


# Dependency to get project state
async def get_project_state(session_id: str) -> ProjectState:
    """Get project state for the session"""
    project_state = session_manager.get_session(session_id)
    if not project_state:
        raise HTTPException(status_code=404, detail="Project not found")
    return project_state


@router.post("/plan", response_model=TaskPlanResponse)
async def generate_task_plan(
    session_id: str,
    request: TaskPlanRequest,
    project_state: ProjectState = Depends(get_project_state)
):
    """
    Generate a task plan for a given goal.
    
    This endpoint demonstrates TaskExecutionAgent integration while providing
    a REST API interface for clients that prefer HTTP over WebSocket.
    """
    try:
        # Create TaskExecutionAgent
        agent = TaskExecutionAgent()
        
        # Generate plan with rationale (Gemini Principle 1)
        tasks, rationale = await agent.plan(request.goal, request.context)
        
        # Convert tasks to serializable format
        task_dicts = [task.to_dict() for task in tasks]
        
        return TaskPlanResponse(
            goal=request.goal,
            rationale=rationale,
            tasks=task_dicts,
            task_count=len(tasks)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Plan generation failed: {str(e)}")


@router.post("/execute", response_model=TaskExecutionResponse)
async def execute_task_plan(
    session_id: str,
    request: TaskExecutionRequest,
    project_state: ProjectState = Depends(get_project_state)
):
    """
    Execute a task plan.
    
    This endpoint provides synchronous task execution via REST API.
    For real-time updates, use the WebSocket endpoint instead.
    """
    if not request.approved:
        raise HTTPException(status_code=400, detail="Task plan not approved")

    try:
        # Convert task data back to Task objects
        tasks = [Task(
            id=task_data.get("id"),
            description=task_data.get("description"),
            metadata=task_data.get("metadata", {})
        ) for task_data in request.tasks]

        # Create TaskExecutionAgent and execute
        agent = TaskExecutionAgent()
        result = await agent.execute(tasks, project_state)

        return TaskExecutionResponse(
            success=result.success,
            summary_message=result.summary_message,
            completed_tasks=[task.to_dict() for task in result.completed_tasks],
            failed_task=result.failed_task.to_dict() if result.failed_task else None,
            total_duration_seconds=result.total_duration_seconds,
            suggested_actions=result.to_dict()["suggested_actions"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task execution failed: {str(e)}")


@router.post("/component-search")
async def execute_component_search_task(
    session_id: str,
    project_state: ProjectState = Depends(get_project_state)
):
    """
    Execute component search using TaskExecutionAgent.
    
    This demonstrates wrapping existing ComponentMatcher functionality
    in a task-based execution model.
    """
    try:
        # Create TaskExecutionAgent
        agent = TaskExecutionAgent()
        
        # Generate plan for component search
        goal = "search for components matching project requirements"
        context = {
            "patterns": project_state.target_patterns,
            "project_brief": project_state.project_brief
        }
        
        tasks, rationale = await agent.plan(goal, context)
        
        # Execute the plan
        result = await agent.execute(tasks, project_state)
        
        # For this endpoint, we also update the project state with actual component search results
        # This demonstrates integration with existing modules
        if result.success:
            await _perform_actual_component_search(project_state)
        
        return TaskExecutionResponse(
            success=result.success,
            summary_message=result.summary_message,
            completed_tasks=[task.to_dict() for task in result.completed_tasks],
            failed_task=result.failed_task.to_dict() if result.failed_task else None,
            total_duration_seconds=result.total_duration_seconds,
            suggested_actions=result.to_dict()["suggested_actions"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Component search failed: {str(e)}")


@router.post("/code-generation")
async def execute_code_generation_task(
    session_id: str,
    project_state: ProjectState = Depends(get_project_state)
):
    """
    Execute code generation using TaskExecutionAgent.
    
    This demonstrates how future code generation modules will be
    integrated through the TaskExecutionAgent.
    """
    # Verify we have selected components
    if not project_state.selected_components:
        raise HTTPException(
            status_code=400,
            detail="No components selected. Complete component selection first."
        )

    try:
        
        # Create TaskExecutionAgent
        agent = TaskExecutionAgent()
        
        # Generate plan for code generation
        goal = "generate code for selected components"
        context = {
            "selected_components": project_state.selected_components,
            "target_patterns": project_state.target_patterns,
            "project_brief": project_state.project_brief
        }
        
        tasks, rationale = await agent.plan(goal, context)
        
        # Execute the plan
        result = await agent.execute(tasks, project_state)
        
        return TaskExecutionResponse(
            success=result.success,
            summary_message=result.summary_message,
            completed_tasks=[task.to_dict() for task in result.completed_tasks],
            failed_task=result.failed_task.to_dict() if result.failed_task else None,
            total_duration_seconds=result.total_duration_seconds,
            suggested_actions=result.to_dict()["suggested_actions"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Code generation failed: {str(e)}")


async def _perform_actual_component_search(project_state: ProjectState) -> None:
    """
    Perform actual component search using existing ComponentMatcher.
    
    This demonstrates how TaskExecutionAgent wraps existing functionality
    while maintaining the actual business logic.
    """
    try:
        # TODO: REPLACE_MOCK - Integrate with real ComponentMatcher when available
        # For now, this is a placeholder that demonstrates the integration pattern
        
        # Get brainstorming results
        brainstorming_session = getattr(project_state, 'brainstorming_session', None)
        if not brainstorming_session:
            return
        
        # Create ComponentMatcher (this would use the real implementation)
        component_matcher = ComponentMatcher()
        
        # Extract requirements from brainstorming
        requirements = component_matcher.extract_requirements(brainstorming_session.to_dict())
        
        # Search for components (mock implementation)
        # In real implementation, this would call:
        # recommendations = await component_matcher.find_best_components(requirements)
        
        # Update project state with results
        # project_state.candidate_components = recommendations.get("candidates", {})
        
    except Exception as e:
        # Log error but don't fail the task execution
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Component search integration failed: {e}")


@router.get("/status")
async def get_task_execution_status(
    session_id: str,
    project_state: ProjectState = Depends(get_project_state)
):
    """
    Get current task execution status.
    
    Returns information about active tasks and execution context.
    """
    return {
        "session_id": session_id,
        "has_active_tasks": project_state.active_task_list is not None,
        "active_task_count": len(project_state.active_task_list) if project_state.active_task_list else 0,
        "task_execution_context": project_state.task_execution_context,
        "project_status": project_state.status.value,
        "last_modified": project_state.last_modified.isoformat()
    }
