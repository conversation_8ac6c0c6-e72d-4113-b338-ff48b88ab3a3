@echo off
echo ========================================
echo    CodeQuilter Development Environment
echo ========================================
echo.

REM Set the project directory
set PROJECT_DIR=%~dp0
cd /d "%PROJECT_DIR%"

echo Current directory: %CD%
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if Python quilt environment exists
if not exist "quilt\Scripts\activate.bat" (
    echo ERROR: Python 'quilt' environment not found
    echo Please ensure the quilt virtual environment is set up
    pause
    exit /b 1
)

echo ✅ Node.js version: 
node --version

echo ✅ Python environment: quilt
echo.

REM Install frontend dependencies if node_modules doesn't exist
if not exist "frontend\node_modules" (
    echo 📦 Installing frontend dependencies...
    cd frontend
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
    cd ..
    echo ✅ Frontend dependencies installed
    echo.
) else (
    echo ✅ Frontend dependencies already installed
    echo.
)

echo 🚀 Starting CodeQuilter...
echo.
echo This will open:
echo   - Backend API Server: http://localhost:8000
echo   - Frontend Development Server: http://localhost:3000
echo   - API Documentation: http://localhost:8000/api/docs
echo.
echo Press Ctrl+C in either window to stop the servers
echo.

REM Start backend in a new window
echo 🔧 Starting Backend Server...
start "CodeQuilter Backend" cmd /k "cd /d "%PROJECT_DIR%" && quilt\Scripts\activate.bat && cd backend && uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend in a new window
echo 🎨 Starting Frontend Server...
start "CodeQuilter Frontend" cmd /k "cd /d "%PROJECT_DIR%\frontend" && npm run dev"

REM Wait a moment for frontend to start
timeout /t 5 /nobreak >nul

echo.
echo 🎉 CodeQuilter is starting up!
echo.
echo Opening browser windows...
echo   - Frontend: http://localhost:3000
echo   - API Docs: http://localhost:8000/api/docs
echo.

REM Open browser windows
timeout /t 3 /nobreak >nul
start http://localhost:3000
timeout /t 2 /nobreak >nul
start http://localhost:8000/api/docs

echo.
echo ========================================
echo CodeQuilter is now running!
echo ========================================
echo.
echo To stop CodeQuilter:
echo   1. Close the Backend and Frontend terminal windows
echo   2. Or press Ctrl+C in each terminal window
echo.
echo Useful URLs:
echo   Frontend:     http://localhost:3000
echo   Backend API:  http://localhost:8000
echo   API Docs:     http://localhost:8000/api/docs
echo   Health Check: http://localhost:8000/api/health
echo.

pause
