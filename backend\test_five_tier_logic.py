#!/usr/bin/env python3
"""
Test script to verify the five-tier progressive questioning logic implementation.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from modules.brainstorming import (
    ProgressiveQuestionEngine, 
    BrainstormingSession, 
    QuestionnaireResponse,
    ALL_QUESTIONS,
    CORE_QUESTION_DATABASE,
    PATTERN_SPECIFIC_QUESTIONS,
    OPERATIONAL_READINESS_QUESTIONS
)
from integrations.llm_client import LLMClient


async def test_five_tier_logic():
    """Test the five-tier progressive questioning logic"""
    print("=== Testing Five-Tier Progressive Logic ===\n")
    
    # Verify question database setup
    print(f"📊 Question Database Summary:")
    print(f"   Core questions: {len(CORE_QUESTION_DATABASE)}")
    print(f"   Pattern-specific questions: {len([q for pattern in PATTERN_SPECIFIC_QUESTIONS.values() for q in pattern])}")
    print(f"   Operational questions: {len(OPERATIONAL_READINESS_QUESTIONS)}")
    print(f"   Total questions in ALL_QUESTIONS: {len(ALL_QUESTIONS)}")
    print()
    
    # Create a mock session
    session = BrainstormingSession(session_id='test')
    
    # Create engine with mock LLM client
    llm_client = LLMClient()  # Mock client
    engine = ProgressiveQuestionEngine(llm_client)
    
    print("🔄 Testing Tier 1 & 2: Core & Architecture Questions")
    
    # Should start with core questions
    questions = await engine.get_next_questions(session, max_questions=3)
    print(f"   Questions returned: {len(questions)}")
    for i, q in enumerate(questions, 1):
        print(f"   {i}. {q.id}: {q.text[:60]}...")
    print()
    
    # Add a response that will trigger REST API pattern
    session.add_response(QuestionnaireResponse(
        question_id='communication_style',
        question_text='How will users interact with your application?',
        answer='Web API (REST/GraphQL)',
        confidence=1.0
    ))
    
    # Simulate answering all core required questions
    core_required_questions = [q.id for q in CORE_QUESTION_DATABASE.values() if q.required]
    print(f"📝 Simulating answers to {len(core_required_questions)} core required questions...")
    
    for q_id in core_required_questions:
        if not session.has_answered_question(q_id):
            session.add_response(QuestionnaireResponse(
                question_id=q_id,
                question_text=f'Mock question for {q_id}',
                answer='Mock answer',
                confidence=1.0
            ))
    
    print("🔄 Testing Tier 3: Pattern Deep-Dive Questions")
    
    # Now should get pattern-specific questions (REST API pattern should be detected)
    questions = await engine.get_next_questions(session, max_questions=3)
    print(f"   Questions returned: {len(questions)}")
    for i, q in enumerate(questions, 1):
        print(f"   {i}. {q.id}: {q.text[:60]}...")
    
    # Check if these are actually pattern-specific questions
    pattern_question_ids = [q_id for pattern in PATTERN_SPECIFIC_QUESTIONS.values() for q_id in pattern.keys()]
    pattern_questions_returned = [q for q in questions if q.id in pattern_question_ids]
    print(f"   Pattern-specific questions: {len(pattern_questions_returned)}")
    print()
    
    # Simulate answering pattern questions
    for q in questions:
        if not session.has_answered_question(q.id):
            session.add_response(QuestionnaireResponse(
                question_id=q.id,
                question_text=q.text,
                answer='Mock answer',
                confidence=1.0
            ))
    
    print("🔄 Testing Tier 4: Operational Readiness Questions")
    
    # Now should get operational questions
    questions = await engine.get_next_questions(session, max_questions=3)
    print(f"   Questions returned: {len(questions)}")
    for i, q in enumerate(questions, 1):
        print(f"   {i}. {q.id}: {q.text[:60]}...")
    
    # Check if these are operational questions
    operational_questions_returned = [q for q in questions if q.id in OPERATIONAL_READINESS_QUESTIONS.keys()]
    print(f"   Operational questions: {len(operational_questions_returned)}")
    print()
    
    # Test pattern identification
    response_map = {r.question_id: r.answer for r in session.responses}
    identified_patterns = engine._identify_patterns_from_responses(response_map)
    print(f"🎯 Identified patterns: {identified_patterns}")
    
    print("\n✅ Five-tier logic test completed successfully!")
    print(f"📈 Total responses in session: {len(session.responses)}")


if __name__ == "__main__":
    asyncio.run(test_five_tier_logic())
