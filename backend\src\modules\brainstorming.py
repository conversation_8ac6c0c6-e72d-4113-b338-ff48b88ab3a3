"""
Brainstorming Module - Intelligent conversation engine for CodeQuilter.

Transforms user intent into structured project requirements through progressive
questioning, pattern confidence scoring, and LLM-driven conversation management.

TODO: REPLACE_MOCK - LLM integration uses mock responses for development.
Replace with real LLM API integration in Phase 3.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum

from ..integrations.llm_client import LLMClient
from ..integrations.real_llm_client import create_llm_client
from ..integrations.github_client import GitHubClient
from ..state.patterns import ArchitecturalPattern, PATTERN_PALETTE


class QuestionType(Enum):
    """Types of questions in the brainstorming process"""
    MULTIPLE_CHOICE = "multiple_choice"
    TEXT = "text"
    BOOLEAN = "boolean"
    SCALE = "scale"


class QuestionCategory(Enum):
    """Categories of questions for organizing the questionnaire"""
    PROJECT_IDENTITY = "project_identity"
    RUNTIME_ENVIRONMENT = "runtime_environment"
    DATA_LAYER = "data_layer"
    COMMUNICATION = "communication"
    USER_PREFERENCES = "user_preferences"


@dataclass
class Question:
    """Individual question definition"""
    id: str
    text: str
    question_type: QuestionType
    category: QuestionCategory
    options: List[str] = field(default_factory=list)
    triggers: List[str] = field(default_factory=list)  # Conditions that trigger this question
    skip_conditions: List[str] = field(default_factory=list)  # When to skip this question
    required: bool = True
    help_text: str = ""


@dataclass
class QuestionnaireResponse:
    """User's answer to a specific question"""
    question_id: str
    question_text: str
    answer: str
    confidence: float = 1.0  # User's confidence in their answer (0.0-1.0)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "question_id": self.question_id,
            "question_text": self.question_text,
            "answer": self.answer,
            "confidence": self.confidence,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class PatternConfidence:
    """Confidence score for an architectural pattern"""
    pattern_name: str
    confidence: float  # 0.0-1.0
    reasoning: str
    contributing_factors: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "pattern_name": self.pattern_name,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "contributing_factors": self.contributing_factors
        }


@dataclass
class IntelligentDecision:
    """AI-driven decision with confidence and reasoning"""
    decision_type: str
    recommendation: str
    confidence: float  # 0.0-1.0
    reasoning: str
    alternatives: List[Dict[str, Any]] = field(default_factory=list)
    health_metrics: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "decision_type": self.decision_type,
            "recommendation": self.recommendation,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "alternatives": self.alternatives,
            "health_metrics": self.health_metrics
        }


@dataclass
class BrainstormingSession:
    """Complete brainstorming conversation state"""
    session_id: str
    project_description: str = ""
    responses: List[QuestionnaireResponse] = field(default_factory=list)
    pattern_scores: Dict[str, PatternConfidence] = field(default_factory=dict)
    next_questions: List[str] = field(default_factory=list)
    intelligent_decisions: List[IntelligentDecision] = field(default_factory=list)
    structured_brief: Dict[str, Any] = field(default_factory=dict)
    conversation_complete: bool = False
    llm_context: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    
    def add_response(self, response: QuestionnaireResponse) -> None:
        """Add a response and update timestamp"""
        self.responses.append(response)
        self.last_updated = datetime.now()
    
    def get_response_by_question_id(self, question_id: str) -> Optional[QuestionnaireResponse]:
        """Get response for a specific question"""
        for response in self.responses:
            if response.question_id == question_id:
                return response
        return None
    
    def has_answered_question(self, question_id: str) -> bool:
        """Check if a question has been answered"""
        return self.get_response_by_question_id(question_id) is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "session_id": self.session_id,
            "project_description": self.project_description,
            "responses": [r.to_dict() for r in self.responses],
            "pattern_scores": {k: v.to_dict() for k, v in self.pattern_scores.items()},
            "next_questions": self.next_questions,
            "intelligent_decisions": [d.to_dict() for d in self.intelligent_decisions],
            "structured_brief": self.structured_brief,
            "conversation_complete": self.conversation_complete,
            "llm_context": self.llm_context,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat()
        }


# Enhanced 5-Tier Question Database for Production Brainstorming
# Following strategic decisions for professional-grade application generation

# TIER 1: Core Function & Stack (What the application does)
TIER_1_CORE_FUNCTION = {
    "project_description": Question(
        id="project_description",
        text="What is the one-sentence description of your project?",
        question_type=QuestionType.TEXT,
        category=QuestionCategory.PROJECT_IDENTITY,
        help_text="Describe what your application does in simple terms",
        required=True
    ),
    "primary_features": Question(
        id="primary_features",
        text="What are the 3-5 primary features? (e.g., 'Users can log in', 'Send welcome emails')",
        question_type=QuestionType.TEXT,
        category=QuestionCategory.PROJECT_IDENTITY,
        help_text="List the main things users will be able to do",
        required=True
    ),
    "external_apis": Question(
        id="external_apis",
        text="Will your application integrate with external APIs or services?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.PROJECT_IDENTITY,
        options=[
            "No external integrations",
            "Payment processing (Stripe, PayPal)",
            "Email services (SendGrid, Mailgun)",
            "Cloud storage (AWS S3, Google Cloud)",
            "Social media APIs (Twitter, Facebook)",
            "Other (specify in follow-up)"
        ],
        help_text="This helps us find the right SDKs and integration libraries",
        required=True
    ),
    "developer_preferences": Question(
        id="developer_preferences",
        text="What programming language do you prefer?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.USER_PREFERENCES,
        options=[
            "Python",
            "JavaScript/TypeScript",
            "Java",
            "Go",
            "Let CodeQuilter decide based on project requirements"
        ],
        help_text="This determines the technology stack and component search",
        required=True
    )
}

# TIER 2: Architectural Blueprint (How the application is built)
TIER_2_ARCHITECTURE = {
    "deployment_target": Question(
        id="deployment_target",
        text="Where will this application run?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.RUNTIME_ENVIRONMENT,
        options=[
            "Cloud Server / Virtual Machine",
            "Serverless Platform (AWS Lambda, etc.)",
            "Edge Computing Device (IoT, Raspberry Pi)",
            "User's Local Machine (CLI tool, desktop app)"
        ]
    ),
    "expected_scale": Question(
        id="expected_scale",
        text="What is the expected scale of concurrent users initially?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.RUNTIME_ENVIRONMENT,
        options=[
            "Low (1-100, personal/internal tool)",
            "Medium (100s-1000s, startup/small business)",
            "High (10,000+, requires significant scalability)"
        ]
    ),
    "data_persistence": Question(
        id="data_persistence",
        text="Does your application need to store data permanently?",
        question_type=QuestionType.BOOLEAN,
        category=QuestionCategory.DATA_LAYER,
        help_text="Think about user accounts, content, settings, etc.",
        required=True
    ),
    "communication_style": Question(
        id="communication_style",
        text="How will users interact with your application?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.COMMUNICATION,
        options=[
            "Web API (REST/GraphQL)",
            "Web Interface (HTML pages)",
            "Command Line Interface",
            "Desktop Application",
            "Mobile Application"
        ],
        help_text="This determines the primary architectural patterns",
        required=True
    )
}

# Combine Tier 1 & 2 into core database for backward compatibility
CORE_QUESTION_DATABASE = {**TIER_1_CORE_FUNCTION, **TIER_2_ARCHITECTURE}


def get_question_by_id(question_id: str) -> Optional[Question]:
    """Get a question by its ID"""
    return CORE_QUESTION_DATABASE.get(question_id)


def get_questions_by_category(category: QuestionCategory) -> List[Question]:
    """Get all questions in a specific category"""
    return [q for q in CORE_QUESTION_DATABASE.values() if q.category == category]


def get_all_questions() -> List[Question]:
    """Get all available questions"""
    return list(CORE_QUESTION_DATABASE.values())


# Pattern-specific question banks (Tier 2: Pattern Deep-Dive)
PATTERN_SPECIFIC_QUESTIONS = {
    "rest_api": {
        "authentication_method": Question(
            id="authentication_method",
            text="What authentication method do you prefer for your API?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            category=QuestionCategory.USER_PREFERENCES,
            options=["JWT Tokens", "Session-based", "OAuth2", "API Keys", "No authentication needed"],
            required=False,
            help_text="Consider your security requirements and client types"
        ),
        "api_versioning": Question(
            id="api_versioning",
            text="How will you handle API versioning?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            category=QuestionCategory.COMMUNICATION,
            options=["URL versioning (/v1/users)", "Header versioning", "No versioning needed"],
            required=False,
            help_text="Important for maintaining backward compatibility"
        ),
        "api_documentation": Question(
            id="api_documentation",
            text="What API documentation approach do you prefer?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            category=QuestionCategory.USER_PREFERENCES,
            options=["Auto-generated (OpenAPI/Swagger)", "Manual documentation", "No documentation needed"],
            required=False
        ),
        "caching_strategy": Question(
            id="caching_strategy",
            text="Do you need caching to improve API performance?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            category=QuestionCategory.USER_PREFERENCES,
            options=[
                "No caching needed",
                "In-memory caching (Redis, Memcached)",
                "Database query caching",
                "HTTP response caching"
            ],
            required=False,
            help_text="Caching can significantly improve API response times"
        )
    },
    "message_queue": {
        "queue_persistence": Question(
            id="queue_persistence",
            text="Should messages persist if the system restarts?",
            question_type=QuestionType.BOOLEAN,
            category=QuestionCategory.DATA_LAYER,
            required=False,
            help_text="Important for reliability and message durability"
        ),
        "queue_retry": Question(
            id="queue_retry",
            text="How should failed messages be handled?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            category=QuestionCategory.USER_PREFERENCES,
            options=["Retry automatically", "Send to dead letter queue", "Log and discard"],
            required=False
        ),
        "caching_strategy": Question(
            id="caching_strategy",
            text="Do you need caching for queue results or frequently accessed data?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            category=QuestionCategory.USER_PREFERENCES,
            options=[
                "No caching needed",
                "Cache queue processing results",
                "Cache frequently accessed data",
                "Both result and data caching"
            ],
            required=False,
            help_text="Caching can reduce queue processing time and database load"
        )
    },
    "api_gateway": {
        "gateway_features": Question(
            id="gateway_features",
            text="What gateway features do you need?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            category=QuestionCategory.COMMUNICATION,
            options=["Rate limiting", "Request/response transformation", "Load balancing", "Basic routing only"],
            required=False
        )
    }
}


# Operational readiness question bank (Tier 3: Production Readiness)
OPERATIONAL_READINESS_QUESTIONS = {
    # Category 1: Observability & Health
    "logging_strategy": Question(
        id="logging_strategy",
        text="How will you handle application logging?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.USER_PREFERENCES,
        options=[
            "Simple print() statements / standard output",
            "Structured, machine-readable logs (JSON format)",
            "Send logs to an external service (e.g., Datadog, Splunk, ELK Stack)"
        ],
        required=False,
        help_text="Logging strategy affects debugging and monitoring capabilities"
    ),
    "monitoring_metrics": Question(
        id="monitoring_metrics",
        text="Do you need to monitor key application metrics (e.g., request latency, error rates)?",
        question_type=QuestionType.BOOLEAN,
        category=QuestionCategory.USER_PREFERENCES,
        required=False,
        help_text="Metrics help understand application health and performance"
    ),

    # Category 2: Security & Compliance
    "secrets_management": Question(
        id="secrets_management",
        text="How do you need to manage sensitive information like API keys and passwords?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.USER_PREFERENCES,
        options=[
            "Environment variables",
            "A dedicated secrets management service (e.g., AWS Secrets Manager, HashiCorp Vault)",
            "Encrypted configuration files"
        ],
        required=False,
        help_text="Critical for security - never hardcode secrets in source code"
    ),
    "rate_limiting": Question(
        id="rate_limiting",
        text="Do you need to protect your API from abuse with rate limiting?",
        question_type=QuestionType.BOOLEAN,
        category=QuestionCategory.USER_PREFERENCES,
        required=False,
        help_text="Important for preventing API abuse and ensuring fair usage"
    ),

    # Category 3: Developer Experience & CI/CD
    "build_process": Question(
        id="build_process",
        text="How do you plan to manage the application's dependencies and build process?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.USER_PREFERENCES,
        options=[
            "Manual setup with pip/npm commands",
            "Using a containerized environment (Docker)",
            "A full CI/CD pipeline (e.g., GitHub Actions, GitLab CI)"
        ],
        required=False,
        help_text="Affects reproducibility and deployment reliability"
    ),

    # Category 4: Data Lifecycle Management
    "database_migrations": Question(
        id="database_migrations",
        text="If using a database, how will you manage changes to the database schema over time?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        category=QuestionCategory.DATA_LAYER,
        options=[
            "Manually apply SQL scripts",
            "Use a dedicated database migration tool (e.g., Alembic, Flyway)"
        ],
        required=False,
        triggers=["data_persistence:yes"],
        help_text="Professional standard for managing database schema changes"
    )
}


class PatternConfidenceCalculator:
    """Maps questionnaire responses to architectural pattern confidence scores"""

    def __init__(self):
        # TODO: REPLACE_MOCK - Pattern scoring rules for development
        # Replace with ML-based scoring in Phase 3
        self.scoring_rules = {
            "rest_api": {
                "communication_style": {"Web API (REST/GraphQL)": 0.9},
                "deployment_target": {"Cloud Server / Virtual Machine": 0.7, "Serverless Platform (AWS Lambda, etc.)": 0.8},
                "expected_scale": {"Medium (100s-1000s, startup/small business)": 0.6, "High (10,000+, requires significant scalability)": 0.8}
            },
            "api_gateway": {
                "communication_style": {"Web API (REST/GraphQL)": 0.8},
                "deployment_target": {"Serverless Platform (AWS Lambda, etc.)": 0.9, "Cloud Server / Virtual Machine": 0.7},
                "expected_scale": {"High (10,000+, requires significant scalability)": 0.9, "Medium (100s-1000s, startup/small business)": 0.6}
            },
            "message_queue": {
                "deployment_target": {"Serverless Platform (AWS Lambda, etc.)": 0.8, "Cloud Server / Virtual Machine": 0.7},
                "expected_scale": {"High (10,000+, requires significant scalability)": 0.8, "Medium (100s-1000s, startup/small business)": 0.5}
            },
            "pub_sub": {
                "deployment_target": {"Serverless Platform (AWS Lambda, etc.)": 0.7, "Cloud Server / Virtual Machine": 0.6},
                "expected_scale": {"High (10,000+, requires significant scalability)": 0.8}
            },
            "adapter": {
                "communication_style": {"Web API (REST/GraphQL)": 0.5, "Web Interface (HTML pages)": 0.4},
                "deployment_target": {"Cloud Server / Virtual Machine": 0.6}
            }
        }

    def calculate_pattern_scores(self, responses: List[QuestionnaireResponse]) -> Dict[str, PatternConfidence]:
        """Calculate confidence scores for each pattern based on responses"""
        pattern_scores = {}

        # Create response lookup for easy access
        response_map = {r.question_id: r.answer for r in responses}

        for pattern_name in PATTERN_PALETTE.keys():
            confidence, reasoning, factors = self._calculate_pattern_confidence(
                pattern_name, response_map
            )

            pattern_scores[pattern_name] = PatternConfidence(
                pattern_name=pattern_name,
                confidence=confidence,
                reasoning=reasoning,
                contributing_factors=factors
            )

        return pattern_scores

    def _calculate_pattern_confidence(self, pattern_name: str, response_map: Dict[str, str]) -> tuple[float, str, List[str]]:
        """Calculate confidence for a specific pattern"""
        if pattern_name not in self.scoring_rules:
            return 0.0, f"No scoring rules defined for {pattern_name}", []

        rules = self.scoring_rules[pattern_name]
        total_score = 0.0
        contributing_factors = []

        for question_id, answer_scores in rules.items():
            if question_id in response_map:
                user_answer = response_map[question_id]
                if user_answer in answer_scores:
                    score = answer_scores[user_answer]
                    total_score += score
                    contributing_factors.append(f"{question_id}: {user_answer} (+{score:.1f})")

        # Normalize score (simple average for now)
        final_confidence = min(total_score / len(rules) if rules else 0.0, 1.0)

        reasoning = self._generate_pattern_reasoning(pattern_name, final_confidence, contributing_factors)

        return final_confidence, reasoning, contributing_factors

    def _generate_pattern_reasoning(self, pattern_name: str, confidence: float, factors: List[str]) -> str:
        """Generate human-readable reasoning for pattern confidence"""
        if confidence >= 0.8:
            return f"Strong match for {pattern_name} pattern based on: {', '.join(factors[:2])}"
        elif confidence >= 0.6:
            return f"Good fit for {pattern_name} pattern with some considerations"
        elif confidence >= 0.4:
            return f"Possible fit for {pattern_name} pattern but may need additional components"
        else:
            return f"Low confidence for {pattern_name} pattern based on current requirements"

    def get_recommended_patterns(self, pattern_scores: Dict[str, PatternConfidence], threshold: float = 0.6) -> List[str]:
        """Get patterns above confidence threshold, sorted by confidence"""
        recommended = [
            (name, score.confidence)
            for name, score in pattern_scores.items()
            if score.confidence >= threshold
        ]
        # Sort by confidence descending
        recommended.sort(key=lambda x: x[1], reverse=True)
        return [name for name, _ in recommended]


class ProgressiveQuestionEngine:
    """Determines which questions to ask based on previous answers"""

    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
        self.questions = CORE_QUESTION_DATABASE

    async def get_next_questions(self, session: BrainstormingSession, max_questions: int = 3) -> List[Question]:
        """
        Determine next questions based on:
        1. Required questions not yet asked
        2. Conditional questions triggered by previous answers
        3. LLM-suggested follow-ups
        """
        next_questions = []

        # 1. Get required questions not yet answered
        required_questions = self._get_required_unanswered_questions(session)
        next_questions.extend(required_questions[:max_questions])

        # 2. If we have room and some answers, get conditional questions
        if len(next_questions) < max_questions and session.responses:
            conditional_questions = await self._get_conditional_questions(session)
            remaining_slots = max_questions - len(next_questions)
            next_questions.extend(conditional_questions[:remaining_slots])

        # 3. If still have room, get LLM follow-ups
        if len(next_questions) < max_questions and len(session.responses) >= 2:
            llm_questions = await self._get_llm_follow_up_questions(session)
            remaining_slots = max_questions - len(next_questions)
            next_questions.extend(llm_questions[:remaining_slots])

        return next_questions

    def _get_required_unanswered_questions(self, session: BrainstormingSession) -> List[Question]:
        """Get required questions that haven't been answered yet"""
        unanswered = []
        for question in self.questions.values():
            if question.required and not session.has_answered_question(question.id):
                if not self._should_skip_question(question, session.responses):
                    unanswered.append(question)
        return unanswered

    def _should_skip_question(self, question: Question, responses: List[QuestionnaireResponse]) -> bool:
        """Check if question should be skipped based on previous answers"""
        # TODO: REPLACE_MOCK - Simple skip logic for development
        # Replace with sophisticated conditional logic in Phase 3

        response_map = {r.question_id: r.answer for r in responses}

        for skip_condition in question.skip_conditions:
            # Simple format: "question_id:answer_value"
            if ":" in skip_condition:
                condition_question, condition_answer = skip_condition.split(":", 1)
                if response_map.get(condition_question) == condition_answer:
                    return True

        return False

    async def _get_conditional_questions(self, session: BrainstormingSession) -> List[Question]:
        """Get questions triggered by previous answers"""
        # TODO: REPLACE_MOCK - Simple conditional logic for development
        conditional_questions = []
        response_map = {r.question_id: r.answer for r in session.responses}

        # Example: If they chose "Web API", ask about authentication
        if response_map.get("communication_style") == "Web API (REST/GraphQL)":
            auth_question = Question(
                id="authentication_method",
                text="What authentication method do you prefer?",
                question_type=QuestionType.MULTIPLE_CHOICE,
                category=QuestionCategory.USER_PREFERENCES,
                options=["JWT Tokens", "Session-based", "OAuth2", "API Keys", "No authentication needed"],
                required=False
            )
            if not session.has_answered_question("authentication_method"):
                conditional_questions.append(auth_question)

        return conditional_questions

    async def _get_llm_follow_up_questions(self, session: BrainstormingSession) -> List[Question]:
        """Ask LLM for intelligent follow-up questions"""
        try:
            # Build context for LLM
            context = await self._build_llm_context(session)

            # Create prompt for intelligent question generation
            prompt = f"""Based on this brainstorming session context, suggest 1-2 intelligent follow-up questions:

{context}

Generate questions that would help clarify the project requirements. Focus on:
1. Technical decisions that need clarification
2. Missing requirements that would affect architecture
3. User experience considerations

Respond in JSON format:
{{
  "questions": [
    {{
      "id": "unique_question_id",
      "text": "Question text?",
      "type": "multiple_choice" or "text" or "boolean",
      "category": "project_identity" or "runtime_environment" or "data_layer" or "communication" or "user_preferences",
      "options": ["option1", "option2"] (only for multiple_choice),
      "reasoning": "Why this question is important"
    }}
  ]
}}"""

            # Get LLM response
            response = await self.llm_client.generate_brainstorming_response(prompt, {"task": "question_generation"})

            if response.success:
                # Parse LLM response and convert to Question objects
                return await self._parse_llm_questions(response.data.get("response", ""))
            else:
                print(f"⚠️  LLM question generation failed: {response.data.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"⚠️  Error in LLM question generation: {e}")

        # TODO: REPLACE_MOCK - Fallback to mock questions if LLM fails
        # Remove this fallback once LLM integration is stable
        return await self._get_mock_follow_up_questions(session)

    async def _build_llm_context(self, session: BrainstormingSession) -> str:
        """Build context string for LLM prompts"""
        context_parts = [
            f"Project: {session.project_description}",
            f"Responses so far: {len(session.responses)}",
            ""
        ]

        for response in session.responses:
            context_parts.append(f"Q: {response.question_text}")
            context_parts.append(f"A: {response.answer}")
            context_parts.append("")

        return "\n".join(context_parts)

    async def _parse_llm_questions(self, llm_response: str) -> List[Question]:
        """Parse LLM response into Question objects"""
        import json
        import re

        try:
            # Extract JSON from response (handle markdown code blocks)
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', llm_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # Try to find JSON without code blocks
                json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                else:
                    raise ValueError("No JSON found in LLM response")

            data = json.loads(json_str)
            questions = []

            for q_data in data.get("questions", []):
                question = Question(
                    id=q_data.get("id", f"llm_generated_{len(questions)}"),
                    text=q_data.get("text", ""),
                    question_type=QuestionType(q_data.get("type", "text")),
                    category=QuestionCategory(q_data.get("category", "user_preferences")),
                    options=q_data.get("options", []),
                    required=False,
                    help_text=q_data.get("reasoning", "")
                )
                questions.append(question)

            return questions

        except Exception as e:
            print(f"⚠️  Failed to parse LLM questions: {e}")
            print(f"LLM Response: {llm_response[:200]}...")
            return []

    async def _get_mock_follow_up_questions(self, session: BrainstormingSession) -> List[Question]:
        """TODO: REPLACE_MOCK - Mock follow-up questions fallback"""
        mock_follow_ups = []
        response_map = {r.question_id: r.answer for r in session.responses}

        # Mock intelligent follow-ups based on answers
        if "data_persistence" in response_map and response_map["data_persistence"].lower() in ["yes", "true"]:
            db_question = Question(
                id="database_preference",
                text="What type of database fits your needs?",
                question_type=QuestionType.MULTIPLE_CHOICE,
                category=QuestionCategory.DATA_LAYER,
                options=["PostgreSQL (Relational)", "MongoDB (Document)", "Redis (Key-Value)", "Let CodeQuilter decide"],
                required=False,
                help_text="Consider your data structure and query patterns"
            )
            if not session.has_answered_question("database_preference"):
                mock_follow_ups.append(db_question)

        return mock_follow_ups


class ConversationManager:
    """Manages LLM-driven intelligent conversation flow"""

    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client

    async def build_conversation_context(self, session: BrainstormingSession) -> str:
        """Build LLM context from current session state"""
        context_parts = [
            f"Project Description: {session.project_description}",
            f"Responses so far: {len(session.responses)}",
            ""
        ]

        for response in session.responses:
            context_parts.append(f"Q: {response.question_text}")
            context_parts.append(f"A: {response.answer}")
            context_parts.append("")

        return "\n".join(context_parts)

    async def get_intelligent_follow_up(self, session: BrainstormingSession) -> Dict[str, Any]:
        """Ask LLM for intelligent follow-up based on current answers"""
        try:
            context = await self.build_conversation_context(session)

            # Create intelligent analysis prompt
            prompt = f"""Analyze this brainstorming session and provide intelligent insights:

{context}

Based on the user's responses, provide:
1. Confidence assessment of the project direction
2. Key assumptions you can make
3. What clarifications are still needed
4. Recommended next questions

Respond in JSON format:
{{
  "confidence": 0.85,
  "reasoning": "Clear explanation of project direction",
  "assumptions": ["assumption1", "assumption2"],
  "clarifications_needed": ["clarification1", "clarification2"],
  "next_questions": ["question_id1", "question_id2"],
  "architectural_insights": "Key insights about the architecture"
}}"""

            # Get LLM response
            response = await self.llm_client.generate_brainstorming_response(prompt, {"task": "session_analysis"})

            if response.success:
                return await self._parse_llm_analysis(response.data.get("response", ""))
            else:
                print(f"⚠️  LLM analysis failed: {response.data.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"⚠️  Error in LLM analysis: {e}")

        # TODO: REPLACE_MOCK - Fallback to mock analysis if LLM fails
        return await self._get_mock_analysis(session)

    async def _parse_llm_analysis(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM analysis response"""
        import json
        import re

        try:
            # Extract JSON from response
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', llm_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                else:
                    raise ValueError("No JSON found in LLM response")

            return json.loads(json_str)

        except Exception as e:
            print(f"⚠️  Failed to parse LLM analysis: {e}")
            print(f"LLM Response: {llm_response[:200]}...")
            return await self._get_mock_analysis(None)

    async def _get_mock_analysis(self, session: BrainstormingSession) -> Dict[str, Any]:
        """TODO: REPLACE_MOCK - Mock analysis fallback"""
        if not session:
            return {
                "confidence": 0.5,
                "reasoning": "Fallback analysis due to LLM parsing error",
                "assumptions": [],
                "clarifications_needed": [],
                "next_questions": []
            }

        response_map = {r.question_id: r.answer for r in session.responses}

        mock_analysis = {
            "next_questions": [],
            "confidence": 0.75,
            "reasoning": "Based on the responses, I can see this is a web API project",
            "assumptions": [],
            "clarifications_needed": []
        }

        # Analyze responses for patterns
        if "communication_style" in response_map:
            if "API" in response_map["communication_style"]:
                mock_analysis["assumptions"].append("REST API architecture")
                mock_analysis["clarifications_needed"].append("Authentication method")
                mock_analysis["next_questions"].append("authentication_method")

        if "data_persistence" in response_map:
            if response_map["data_persistence"].lower() in ["yes", "true"]:
                mock_analysis["assumptions"].append("Database required")
                mock_analysis["clarifications_needed"].append("Database type preference")
                mock_analysis["next_questions"].append("database_preference")

        return mock_analysis

    async def generate_confident_assumptions(self, session: BrainstormingSession) -> List[Dict[str, Any]]:
        """Generate assumptions for 80%+ confidence decisions"""
        try:
            context = await self.build_conversation_context(session)

            # Create prompt for confident assumptions
            prompt = f"""Based on this brainstorming session, generate confident technology assumptions:

{context}

For decisions where you have 80%+ confidence, suggest specific technologies with reasoning.
Focus on:
1. Programming languages and frameworks
2. Databases and storage
3. Deployment and infrastructure
4. Authentication and security

Respond in JSON format:
{{
  "assumptions": [
    {{
      "assumption": "Specific technology recommendation",
      "confidence": 0.85,
      "reasoning": "Why this is the best choice",
      "alternatives": ["alternative1", "alternative2"],
      "category": "framework" or "database" or "deployment" or "authentication"
    }}
  ]
}}"""

            # Get LLM response
            response = await self.llm_client.generate_brainstorming_response(prompt, {"task": "confident_assumptions"})

            if response.success:
                return await self._parse_llm_assumptions(response.data.get("response", ""))
            else:
                print(f"⚠️  LLM assumptions failed: {response.data.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"⚠️  Error in LLM assumptions: {e}")

        # TODO: REPLACE_MOCK - Fallback to mock assumptions if LLM fails
        return await self._get_mock_assumptions(session)

    async def _parse_llm_assumptions(self, llm_response: str) -> List[Dict[str, Any]]:
        """Parse LLM assumptions response"""
        import json
        import re

        try:
            # Extract JSON from response
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', llm_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                else:
                    raise ValueError("No JSON found in LLM response")

            data = json.loads(json_str)
            return data.get("assumptions", [])

        except Exception as e:
            print(f"⚠️  Failed to parse LLM assumptions: {e}")
            print(f"LLM Response: {llm_response[:200]}...")
            return []

    async def _get_mock_assumptions(self, session: BrainstormingSession) -> List[Dict[str, Any]]:
        """TODO: REPLACE_MOCK - Mock assumptions fallback"""
        assumptions = []
        response_map = {r.question_id: r.answer for r in session.responses}

        # Generate mock assumptions based on common patterns
        if "communication_style" in response_map:
            if "Web API" in response_map["communication_style"]:
                assumptions.append({
                    "assumption": "FastAPI for Python web framework",
                    "confidence": 0.85,
                    "reasoning": "Most modern Python API framework with excellent performance and documentation",
                    "alternatives": ["Flask", "Django REST Framework"],
                    "category": "framework"
                })

        if "deployment_target" in response_map:
            if "Cloud Server" in response_map["deployment_target"]:
                assumptions.append({
                    "assumption": "Docker containerization",
                    "confidence": 0.82,
                    "reasoning": "Standard for cloud deployment, ensures consistency across environments",
                    "alternatives": ["Direct deployment", "Virtual environments"],
                    "category": "deployment"
                })

        return assumptions


class DecisionEngine:
    """Implements 'CodeQuilter Decide' logic with component availability"""

    def __init__(self, github_client: GitHubClient, llm_client: LLMClient):
        self.github_client = github_client
        self.llm_client = llm_client

    async def make_intelligent_decision(self, decision_type: str, context: Dict[str, Any]) -> IntelligentDecision:
        """
        Make data-driven decisions based on:
        1. License compatibility (hard filter)
        2. Project health score (40% stars, 40% activity, 20% community)
        3. Integration complexity (pattern-based heuristics)
        """
        # TODO: REPLACE_MOCK - Mock decision making for development
        # Replace with real component analysis in Phase 3

        if decision_type == "database":
            return await self._decide_database(context)
        elif decision_type == "web_framework":
            return await self._decide_web_framework(context)
        elif decision_type == "authentication":
            return await self._decide_authentication(context)
        else:
            return IntelligentDecision(
                decision_type=decision_type,
                recommendation="No recommendation available",
                confidence=0.0,
                reasoning="Unknown decision type"
            )

    async def _decide_database(self, context: Dict[str, Any]) -> IntelligentDecision:
        """Make database selection decision"""
        # TODO: REPLACE_MOCK - Mock health metrics for popular databases
        # Replace with real GitHub API integration for component health scoring
        options = {
            "PostgreSQL": {"stars": 15000, "last_commit": "2024-12-01", "health_score": 0.92},
            "MongoDB": {"stars": 25000, "last_commit": "2024-11-28", "health_score": 0.88},
            "Redis": {"stars": 65000, "last_commit": "2024-12-02", "health_score": 0.95}
        }

        # Simple decision logic based on context
        scale = context.get("expected_scale", "")
        if "High" in scale:
            recommendation = "PostgreSQL"
            reasoning = "PostgreSQL offers excellent performance and ACID compliance for high-scale applications"
        else:
            recommendation = "PostgreSQL"
            reasoning = "PostgreSQL is the most versatile choice for most applications"

        alternatives = [
            {"name": "MongoDB", "score": 0.75, "reason": "Good for document-based data"},
            {"name": "Redis", "score": 0.65, "reason": "Excellent for caching and simple key-value storage"}
        ]

        return IntelligentDecision(
            decision_type="database",
            recommendation=recommendation,
            confidence=0.87,
            reasoning=reasoning,
            alternatives=alternatives,
            health_metrics=options[recommendation]
        )

    async def _decide_web_framework(self, context: Dict[str, Any]) -> IntelligentDecision:
        """Make web framework selection decision"""
        # TODO: REPLACE_MOCK - Mock decision for Python web frameworks
        # Replace with real component analysis and health metrics
        return IntelligentDecision(
            decision_type="web_framework",
            recommendation="FastAPI",
            confidence=0.89,
            reasoning="FastAPI provides excellent performance, automatic API documentation, and modern Python features",
            alternatives=[
                {"name": "Flask", "score": 0.72, "reason": "Lightweight and flexible"},
                {"name": "Django", "score": 0.68, "reason": "Full-featured but heavier"}
            ],
            health_metrics={"stars": 75000, "last_commit": "2024-12-01", "health_score": 0.94}
        )

    async def _decide_authentication(self, context: Dict[str, Any]) -> IntelligentDecision:
        """Make authentication method decision"""
        scale = context.get("expected_scale", "")

        if "High" in scale:
            recommendation = "OAuth2 with JWT"
            confidence = 0.85
            reasoning = "OAuth2 with JWT tokens provides scalable, stateless authentication suitable for high-traffic applications"
        else:
            recommendation = "JWT Tokens"
            confidence = 0.82
            reasoning = "JWT tokens provide a good balance of security and simplicity for most applications"

        return IntelligentDecision(
            decision_type="authentication",
            recommendation=recommendation,
            confidence=confidence,
            reasoning=reasoning,
            alternatives=[
                {"name": "Session-based", "score": 0.65, "reason": "Simple but requires server state"},
                {"name": "API Keys", "score": 0.55, "reason": "Good for service-to-service communication"}
            ]
        )


class BrainstormingEngine:
    """Main orchestrator for the brainstorming process"""

    def __init__(self, llm_client=None, github_client=None, use_real_llm: bool = True):
        # Use real LLM client by default, fallback to mock if needed
        if llm_client is None:
            try:
                self.llm_client = create_llm_client(use_real_api=use_real_llm)
                print(f"🤖 BrainstormingEngine using {'real' if use_real_llm else 'mock'} LLM client")
            except Exception as e:
                print(f"⚠️  Failed to create real LLM client: {e}")
                print("🔄 Falling back to mock LLM client")
                self.llm_client = LLMClient()  # Fallback to mock
        else:
            self.llm_client = llm_client

        # TODO: REPLACE_MOCK - GitHub client integration for component health
        if github_client is None:
            from ..integrations.github_client import GitHubClient
            self.github_client = GitHubClient()  # Mock client for now
        else:
            self.github_client = github_client

        self.pattern_calculator = PatternConfidenceCalculator()
        self.question_engine = ProgressiveQuestionEngine(self.llm_client)
        self.conversation_manager = ConversationManager(self.llm_client)
        self.decision_engine = DecisionEngine(self.github_client, self.llm_client)

    async def start_brainstorming(self, session_id: str, initial_description: str = "") -> BrainstormingSession:
        """Initialize a new brainstorming session"""
        session = BrainstormingSession(
            session_id=session_id,
            project_description=initial_description
        )

        # If we have an initial description, automatically add it as the first response
        if initial_description.strip():
            project_desc_question = get_question_by_id("project_description")
            if project_desc_question:
                initial_response = QuestionnaireResponse(
                    question_id="project_description",
                    question_text=project_desc_question.text,
                    answer=initial_description,
                    confidence=1.0
                )
                session.add_response(initial_response)

                # Calculate initial pattern scores
                session.pattern_scores = self.pattern_calculator.calculate_pattern_scores(session.responses)

        # Get initial questions (excluding project_description since it's already answered)
        initial_questions = await self.question_engine.get_next_questions(session, max_questions=3)
        session.next_questions = [q.id for q in initial_questions]

        return session

    async def process_answer(self, session: BrainstormingSession, question_id: str, answer: str, confidence: float = 1.0) -> BrainstormingSession:
        """Process a user's answer and update the session"""
        # Get the question details
        question = get_question_by_id(question_id)
        if not question:
            raise ValueError(f"Unknown question ID: {question_id}")

        # Add the response
        response = QuestionnaireResponse(
            question_id=question_id,
            question_text=question.text,
            answer=answer,
            confidence=confidence
        )
        session.add_response(response)

        # Recalculate pattern scores
        session.pattern_scores = self.pattern_calculator.calculate_pattern_scores(session.responses)

        # Get next questions
        next_questions = await self.question_engine.get_next_questions(session, max_questions=3)
        session.next_questions = [q.id for q in next_questions]

        # Check if we should make any confident decisions
        await self._make_confident_decisions(session)

        # Check if conversation is complete
        session.conversation_complete = await self._is_conversation_complete(session)

        if session.conversation_complete:
            session.structured_brief = await self._generate_structured_brief(session)

        return session

    async def _make_confident_decisions(self, session: BrainstormingSession) -> None:
        """Make confident decisions based on current responses"""
        response_map = {r.question_id: r.answer for r in session.responses}

        # Make decisions for high-confidence patterns
        high_confidence_patterns = [
            name for name, pattern_conf in session.pattern_scores.items()
            if pattern_conf.confidence >= 0.8
        ]

        for pattern_name in high_confidence_patterns:
            # Make component decisions for this pattern
            if pattern_name == "rest_api" and "communication_style" in response_map:
                decision = await self.decision_engine.make_intelligent_decision(
                    "web_framework", response_map
                )
                session.intelligent_decisions.append(decision)

            if "data_persistence" in response_map and response_map["data_persistence"].lower() in ["yes", "true"]:
                decision = await self.decision_engine.make_intelligent_decision(
                    "database", response_map
                )
                session.intelligent_decisions.append(decision)

    async def _is_conversation_complete(self, session: BrainstormingSession) -> bool:
        """Determine if the brainstorming conversation is complete"""
        # Require minimum number of responses for proper brainstorming
        min_responses = 8  # Increased from 4 to ensure thorough brainstorming
        has_core_info = len(session.responses) >= min_responses

        # Check if we have at least one high-confidence pattern
        has_confident_pattern = any(
            pattern_conf.confidence >= 0.7  # Increased threshold from 0.6 to 0.7
            for pattern_conf in session.pattern_scores.values()
        )

        # ALL required questions must be answered (not just 80%)
        required_questions = [q for q in CORE_QUESTION_DATABASE.values() if q.required]
        answered_required = sum(
            1 for q in required_questions
            if session.has_answered_question(q.id)
        )
        has_required_answers = answered_required >= len(required_questions)  # 100% of required questions

        # Also check that we have no more questions to ask
        next_questions = await self.question_engine.get_next_questions(session, max_questions=1)
        no_more_questions = len(next_questions) == 0

        # All criteria must be met for completion
        return has_core_info and has_confident_pattern and has_required_answers and no_more_questions

    async def _generate_structured_brief(self, session: BrainstormingSession) -> Dict[str, Any]:
        """Generate the final structured brief for the project"""
        # Get recommended patterns
        recommended_patterns = self.pattern_calculator.get_recommended_patterns(
            session.pattern_scores, threshold=0.6
        )

        # Organize responses by category
        responses_by_category = {}
        for response in session.responses:
            question = get_question_by_id(response.question_id)
            if question:
                category = question.category.value
                if category not in responses_by_category:
                    responses_by_category[category] = []
                responses_by_category[category].append({
                    "question": response.question_text,
                    "answer": response.answer,
                    "confidence": response.confidence
                })

        # Build structured brief
        brief = {
            "project_overview": {
                "description": session.project_description,
                "created_at": session.created_at.isoformat(),
                "total_responses": len(session.responses)
            },
            "recommended_patterns": [
                {
                    "name": pattern_name,
                    "confidence": session.pattern_scores[pattern_name].confidence,
                    "reasoning": session.pattern_scores[pattern_name].reasoning
                }
                for pattern_name in recommended_patterns
            ],
            "intelligent_decisions": [decision.to_dict() for decision in session.intelligent_decisions],
            "user_responses": responses_by_category,
            "next_steps": [
                "Component discovery for recommended patterns",
                "Architecture validation",
                "Code generation planning"
            ]
        }

        return brief

    async def get_session_status(self, session: BrainstormingSession) -> Dict[str, Any]:
        """Get current status of brainstorming session"""
        progress = len(session.responses) / max(len(CORE_QUESTION_DATABASE), 1)

        return {
            "session_id": session.session_id,
            "progress": min(progress, 1.0),
            "responses_count": len(session.responses),
            "patterns_identified": len([p for p in session.pattern_scores.values() if p.confidence > 0.5]),
            "decisions_made": len(session.intelligent_decisions),
            "conversation_complete": session.conversation_complete,
            "next_questions_count": len(session.next_questions),
            "last_updated": session.last_updated.isoformat()
        }
