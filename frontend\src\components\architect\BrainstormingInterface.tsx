/**
 * BrainstormingInterface - Interactive Q&A component for the brainstorming flow
 * 
 * Handles the complete brainstorming conversation including:
 * - Displaying questions with different types (TEXT, MULTIPLE_CHOICE, BOOLEAN)
 * - Collecting user answers with confidence levels
 * - Showing progress through the question flow
 * - Transitioning to component discovery when complete
 */

import React, { useState, useEffect } from 'react';
import { useProjectStore } from '../../stores/projectStore';
import { <PERSON>ton, Card, CardHeader, CardTitle, CardContent, Badge, LoadingSpinner } from '../ui';
import { CheckCircle, ArrowRight, HelpCircle, Brain } from 'lucide-react';

interface Question {
  id: string;
  text: string;
  question_type: string;
  category: string;
  options: string[];
  required: boolean;
  help_text: string;
}

interface BrainstormingInterfaceProps {
  sessionId: string;
}

export const BrainstormingInterface: React.FC<BrainstormingInterfaceProps> = ({ sessionId }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [confidence, setConfidence] = useState<Record<string, number>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showHelp, setShowHelp] = useState<Record<string, boolean>>({});

  const {
    projectState,
    isLoading,
    error,
    submitAnswer,
    completeBrainstorming,
    searchComponents,
  } = useProjectStore();

  const brainstormingSession = projectState?.brainstorming_session;
  const questions = brainstormingSession?.next_questions || [];
  const currentQuestion = questions[currentQuestionIndex];

  // Auto-advance when no more questions
  useEffect(() => {
    if (brainstormingSession?.conversation_complete) {
      handleCompleteBrainstorming();
    }
  }, [brainstormingSession?.conversation_complete]);

  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers(prev => ({ ...prev, [questionId]: answer }));
    // Set default confidence
    if (!confidence[questionId]) {
      setConfidence(prev => ({ ...prev, [questionId]: 1.0 }));
    }
  };

  const handleConfidenceChange = (questionId: string, conf: number) => {
    setConfidence(prev => ({ ...prev, [questionId]: conf }));
  };

  const handleSubmitAnswer = async () => {
    if (!currentQuestion || !answers[currentQuestion.id]) return;

    setIsSubmitting(true);
    try {
      await submitAnswer(
        currentQuestion.id,
        answers[currentQuestion.id]
      );
      
      // Move to next question or complete
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(prev => prev + 1);
      }
    } catch (err) {
      console.error('Failed to submit answer:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCompleteBrainstorming = async () => {
    try {
      await completeBrainstorming();
      // Automatically start component discovery
      await searchComponents();
    } catch (err) {
      console.error('Failed to complete brainstorming:', err);
    }
  };

  const toggleHelp = (questionId: string) => {
    setShowHelp(prev => ({ ...prev, [questionId]: !prev[questionId] }));
  };

  const renderQuestionInput = (question: Question) => {
    const questionId = question.id;
    const currentAnswer = answers[questionId] || '';

    switch (question.question_type) {
      case 'TEXT':
        return (
          <textarea
            value={currentAnswer}
            onChange={(e) => handleAnswerChange(questionId, e.target.value)}
            placeholder="Enter your answer..."
            className="w-full rounded-md border border-gray-300 p-3 text-sm resize-none focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
            rows={3}
            autoFocus
          />
        );

      case 'MULTIPLE_CHOICE':
        return (
          <div className="space-y-2">
            {question.options.map((option, index) => (
              <label key={index} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name={questionId}
                  value={option}
                  checked={currentAnswer === option}
                  onChange={(e) => handleAnswerChange(questionId, e.target.value)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="text-sm text-gray-700">{option}</span>
              </label>
            ))}
          </div>
        );

      case 'BOOLEAN':
        return (
          <div className="space-y-2">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name={questionId}
                value="yes"
                checked={currentAnswer === 'yes'}
                onChange={(e) => handleAnswerChange(questionId, e.target.value)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
              />
              <span className="text-sm text-gray-700">Yes</span>
            </label>
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name={questionId}
                value="no"
                checked={currentAnswer === 'no'}
                onChange={(e) => handleAnswerChange(questionId, e.target.value)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
              />
              <span className="text-sm text-gray-700">No</span>
            </label>
          </div>
        );

      default:
        return (
          <input
            type="text"
            value={currentAnswer}
            onChange={(e) => handleAnswerChange(questionId, e.target.value)}
            placeholder="Enter your answer..."
            className="w-full rounded-md border border-gray-300 p-3 text-sm focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
            autoFocus
          />
        );
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner size="sm" />
          <span className="ml-2 text-sm text-gray-600">Loading brainstorming session...</span>
        </CardContent>
      </Card>
    );
  }

  if (!currentQuestion) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-success-500" />
            <span>Brainstorming Complete</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-4">
            Great! I've gathered enough information about your project. Moving to component discovery...
          </p>
          <Button onClick={handleCompleteBrainstorming} disabled={isLoading}>
            <ArrowRight className="mr-2 h-4 w-4" />
            Continue to Component Discovery
          </Button>
        </CardContent>
      </Card>
    );
  }

  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
  const answeredCount = Object.keys(answers).length;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-primary-500" />
          <span>Brainstorming Session</span>
          <Badge variant="info">Question {currentQuestionIndex + 1} of {questions.length}</Badge>
        </CardTitle>
        
        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
        <p className="text-xs text-gray-500">
          {answeredCount} questions answered • {questions.length - currentQuestionIndex} remaining
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Question */}
        <div>
          <div className="flex items-start justify-between mb-3">
            <h3 className="text-lg font-medium text-gray-900 leading-relaxed">
              {currentQuestion.text}
            </h3>
            {currentQuestion.help_text && (
              <button
                onClick={() => toggleHelp(currentQuestion.id)}
                className="ml-2 text-gray-400 hover:text-gray-600"
                title="Show help"
              >
                <HelpCircle className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Help text */}
          {showHelp[currentQuestion.id] && currentQuestion.help_text && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-800">{currentQuestion.help_text}</p>
            </div>
          )}

          {/* Question input */}
          <div className="mb-4">
            {renderQuestionInput(currentQuestion)}
          </div>

          {/* Required indicator */}
          {currentQuestion.required && (
            <p className="text-xs text-gray-500 mb-4">* This question is required</p>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            Category: <span className="capitalize">{currentQuestion.category.replace('_', ' ')}</span>
          </div>
          
          <div className="flex space-x-3">
            {currentQuestionIndex > 0 && (
              <Button 
                variant="outline" 
                onClick={() => setCurrentQuestionIndex(prev => prev - 1)}
                disabled={isSubmitting}
              >
                Previous
              </Button>
            )}
            
            <Button 
              onClick={handleSubmitAnswer}
              disabled={!answers[currentQuestion.id] || isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span className="ml-2">Submitting...</span>
                </>
              ) : (
                <>
                  <span>
                    {currentQuestionIndex === questions.length - 1 ? 'Complete' : 'Next'}
                  </span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Error display */}
        {error && (
          <div className="mt-4 p-3 bg-error-50 border border-error-200 rounded-md">
            <p className="text-sm text-error-800">{error}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
