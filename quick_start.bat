@echo off
echo Starting CodeQuilter (Quick Start)...

REM Set project directory
set PROJECT_DIR=%~dp0
cd /d "%PROJECT_DIR%"

REM Start backend
start "Backend" cmd /k "quilt\Scripts\activate.bat && cd backend && uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload"

REM Wait and start frontend
timeout /t 3 /nobreak >nul
start "Frontend" cmd /k "cd frontend && npm run dev"

REM Open browser
timeout /t 5 /nobreak >nul
start http://localhost:3000

echo CodeQuilter started! Check the terminal windows.
pause
